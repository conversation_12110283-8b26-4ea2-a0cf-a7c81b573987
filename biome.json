{"$schema": "https://biomejs.dev/schemas/2.1.3/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "includes": ["**", "!**/node_modules", "!**/android", "!**/ios", "!**/web"]}, "formatter": {"enabled": true, "indentStyle": "space", "lineWidth": 120}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"useKeyWithClickEvents": "off"}, "suspicious": {"noArrayIndexKey": "off"}, "complexity": {"noUselessFragments": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}