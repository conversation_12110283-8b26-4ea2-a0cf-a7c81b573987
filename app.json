{"name": "DeadlockStats", "slug": "DeadlockStats", "scheme": "deadlockstats", "version": "0.0.1", "orientation": "portrait", "userInterfaceStyle": "automatic", "icon": "./assets/images/app-icon-all.png", "updates": {"fallbackToCacheTimeout": 0}, "newArchEnabled": true, "jsEngine": "hermes", "assetBundlePatterns": ["**/*"], "android": {"icon": "./assets/images/app-icon-android-legacy.png", "package": "com.deadlockapi.deadlockstats", "adaptiveIcon": {"foregroundImage": "./assets/images/app-icon-android-adaptive-foreground.png", "backgroundImage": "./assets/images/app-icon-android-adaptive-background.png"}, "allowBackup": false, "edgeToEdgeEnabled": true, "versionCode": 1}, "ios": {"icon": "./assets/images/app-icon-ios.png", "supportsTablet": true, "bundleIdentifier": "com.deadlockapi.deadlockstats", "buildNumber": "0.0.1"}, "web": {"favicon": "./assets/images/app-icon-web-favicon.png", "bundler": "metro"}, "plugins": ["expo-localization", "expo-font", ["expo-splash-screen", {"image": "./assets/images/app-icon-android-adaptive-foreground.png", "imageWidth": 200, "resizeMode": "cover", "backgroundColor": "#191015"}], ["react-native-edge-to-edge", {"android": {"parentTheme": "Light", "enforceNavigationBarContrast": false}}]], "experiments": {"tsconfigPaths": true}, "extra": {"ignite": {"version": "11.0.1"}, "eas": {"projectId": "74b94e72-decb-4b89-8618-29bfd9ff8362"}}}