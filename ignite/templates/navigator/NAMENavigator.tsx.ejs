---
destinationDir: app/navigators
---
import { createNativeStackNavigator } from "@react-navigation/native-stack"
import { WelcomeScreen } from "@/screens/WelcomeScreen"

export type <%= props.pascalCaseName %>NavigatorParamList = {
  Demo: undefined
}

const Stack = createNativeStackNavigator<<%= props.pascalCaseName %>NavigatorParamList>()
export const <%= props.pascalCaseName %>Navigator = () => {
  return (
    <Stack.Navigator screenOptions={{ cardStyle: { backgroundColor: "transparent" }, headerShown: false, }}>
      <Stack.Screen name="Demo" component={WelcomeScreen} />
    </Stack.Navigator>
  )
}
